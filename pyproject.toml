[project]
name = "invoice-agent"
version = "0.1.0"
description = "Invoice Agent System"
authors = [
    {name = "Your Name", email = "<EMAIL>"},
]
dependencies = [
    "fastapi>=0.100.0",
    "uvicorn[standard]>=0.20.0",
    "sqlalchemy>=2.0.0",
    "psycopg[binary]>=3.1.0",
    "openpyxl>=3.1.0",
    "alembic>=1.13.0",
    "typing-extensions>=4.0.0",
    "Mako>=1.1.0",
    "Pygments>=2.10.0",
    "MarkupSafe>=2.0.0",
    "pytesseract>=0.3.10",
    "Pillow>=10.0.0",
    "pdf2image>=1.17.0",
    "openai>=1.0.0",
    "python-dotenv>=1.0.0",
    "thefuzz[speedup]>=0.20.0",
    "python-multipart>=0.0.6",
    "requests>=2.32.3",
]
requires-python = ">=3.8"
readme = "README.md"
license = {text = "MIT"}

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "black>=23.0.0",
    "isort>=5.0.0",
    "mypy>=1.0.0",
]

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]
where = ["."]
include = ["app*"]

