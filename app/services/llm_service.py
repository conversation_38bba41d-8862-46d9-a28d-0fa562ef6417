import os
import json
from openai import OpenAI, APIConnectionError, RateLimitError, APIStatusError
from pydantic import ValidationError
from app.schemas import ExtractedOrderData
from dotenv import load_dotenv

# 加载 .env 文件中的环境变量
load_dotenv()

# API 密钥管理：通过环境变量读取 OpenAI API 密钥
# 请确保在您的环境中设置了 OPENAI_API_KEY
# 例如: export OPENAI_API_KEY='your_api_key_here'
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
OPENAI_API_BASE = os.getenv("OPENAI_API_BASE") # 允许用户配置自定义 OpenAI API 地址
LLM_HTTP_PROXY = os.getenv("LLM_HTTP_PROXY") # 用户指定的代理

if not OPENAI_API_KEY:
    print("警告: OPENAI_API_KEY 环境变量未设置。LLM 服务可能无法正常工作。")

# 根据是否存在代理配置 OpenAI 客户端
if LLM_HTTP_PROXY:
    client = OpenAI(
        api_key=OPENAI_API_KEY,
        base_url=OPENAI_API_BASE,
        http_client=OpenAI.Client(proxies=LLM_HTTP_PROXY) # type: ignore
    )
else:
    client = OpenAI(
        api_key=OPENAI_API_KEY,
        base_url=OPENAI_API_BASE
    )


# 选用 OpenAI 模型
# 优先考虑 gpt-3.5-turbo 或 gpt-4o-mini (如果可用且成本效益高)
# 这里我们选择 gpt-3.5-turbo 作为示例，您可以根据需要更改
LLM_MODEL = os.getenv("LLM_MODEL", "gpt-3.5-turbo")

def build_llm_prompt(text: str) -> str:
    """
    构建用于 LLM 提取结构化数据的 prompt。
    """
    # Prompt 工程：精心设计 prompt 以指导 LLM 准确提取所需信息，并以期望的 JSON 格式输出。
    # 考虑到订单文本的多样性（手写、打印、不同布局），prompt 需要具有一定的鲁棒性。
    prompt = f"""
从以下订单文本中提取结构化信息。请确保提取以下字段：
- customer_name (字符串)
- order_date (字符串, 格式尽量为 YYYY-MM-DD)
- items (数组，每个元素包含):
    - original_input (字符串, 客户在订单上原始输入的产品名称/描述)
    - quantity (整数)

请将提取的信息以 JSON 格式返回。JSON 结构应如下所示：
{{
  "customer_name": "客户名称",
  "order_date": "YYYY-MM-DD",
  "items": [
    {{
      "original_input": "产品原始输入1",
      "quantity": 数量1
    }},
    {{
      "original_input": "产品原始输入2",
      "quantity": 数量2
    }}
  ]
}}

如果某些信息在文本中找不到，请在相应的字段中使用 null 或者省略该字段（如果是可选的）。
订单日期如果格式不明确，请尝试转换为 YYYY-MM-DD，如果无法转换，则保留原始格式。
确保 `items` 列表中的 `quantity` 是整数。

订单文本如下：
---
{text}
---

请严格按照上述 JSON 格式返回结果。
"""
    return prompt

async def extract_structured_data_from_text(text: str) -> ExtractedOrderData:
    """
    使用 LLM 从文本中提取结构化订单信息。
    """
    if not OPENAI_API_KEY:
        raise ValueError("OPENAI_API_KEY 环境变量未设置。无法调用 LLM 服务。")

    prompt = build_llm_prompt(text)

    try:
        response = await client.chat.completions.create( # type: ignore
            model=LLM_MODEL,
            messages=[
                {"role": "system", "content": "你是一个帮助从文本中提取结构化订单信息的 AI 助手。"},
                {"role": "user", "content": prompt}
            ],
            temperature=0.2, # 较低的 temperature 使输出更具确定性
            response_format={"type": "json_object"} # 请求 JSON 输出
        )

        # response.choices[0].message.content 类型是 str | None
        llm_output_content = response.choices[0].message.content
        if not llm_output_content:
            raise ValueError("LLM 返回了空内容。")

        # 尝试解析 LLM 输出的 JSON
        try:
            extracted_data_dict = json.loads(llm_output_content)
        except json.JSONDecodeError as e:
            print(f"LLM 输出的 JSON 解析失败: {e}")
            print(f"LLM 原始输出: {llm_output_content}")
            raise ValueError(f"LLM 返回的不是有效的 JSON 格式。原始输出: {llm_output_content}")

        # 使用 Pydantic 模型验证提取的数据
        try:
            validated_data = ExtractedOrderData(**extracted_data_dict)
            return validated_data
        except ValidationError as e:
            print(f"LLM 输出的数据结构验证失败: {e}")
            print(f"LLM 解析后的字典: {extracted_data_dict}")
            # 可以考虑在这里进行一些补救措施，或者直接抛出错误
            # 例如，如果缺少关键字段，可以尝试填充默认值或提示用户
            missing_fields = []
            for error in e.errors():
                missing_fields.extend(error['loc'])
            
            error_message = f"LLM 返回的数据缺少关键字段或格式不正确: {', '.join(map(str, set(missing_fields)))}."
            # 尝试从原始数据中提取部分可用信息，或者根据业务逻辑决定如何处理
            # 这里简单地重新抛出错误，让上层处理
            raise ValueError(error_message + f" Pydantic 错误: {e.errors()}")


    except APIConnectionError as e:
        print(f"无法连接到 OpenAI API: {e}")
        raise ConnectionError(f"无法连接到 OpenAI API: {e}") # type: ignore
    except RateLimitError as e:
        print(f"OpenAI API 请求已达到速率限制: {e}")
        raise Exception(f"OpenAI API 请求已达到速率限制: {e}") # type: ignore
    except APIStatusError as e:
        print(f"OpenAI API 返回错误状态: {e.status_code} - {e.response}")
        error_detail = f"OpenAI API 错误: {e.status_code}."
        if e.body and 'message' in e.body: # type: ignore
            error_detail += f" 详情: {e.body['message']}" # type: ignore
        elif e.body and 'error' in e.body and isinstance(e.body['error'], dict) and 'message' in e.body['error']: # type: ignore
             error_detail += f" 详情: {e.body['error']['message']}" # type: ignore
        raise Exception(error_detail) # type: ignore
    except Exception as e:
        print(f"调用 LLM 服务时发生未知错误: {e}")
        raise Exception(f"调用 LLM 服务时发生未知错误: {e}") # type: ignore