from sqlalchemy.orm import Session
from thefuzz import fuzz
from typing import List, Dict, Any
from app import models, schemas

# 定义高置信度匹配阈值
HIGH_CONFIDENCE_THRESHOLD = 0.8
# 定义最低匹配阈值
MINIMUM_MATCH_THRESHOLD = 0.3 # 示例值，可以根据实际情况调整

def get_all_products(db: Session) -> List[models.Product]:
    """
    从数据库中获取所有产品信息。
    """
    return db.query(models.Product).all()

def match_products_for_order(order_data: schemas.OrderProcessRequest, db: Session) -> Dict[str, Any]:
    """
    将订单项目与数据库中的产品进行模糊匹配。
    """
    db_products = get_all_products(db)
    processed_items = []
    all_items_high_confidence = True

    for item in order_data.items:
        best_match = None
        highest_score = 0

        if not item.original_input or not item.original_input.strip():
            # 如果 original_input 为空或仅包含空格，则标记为低置信度
            processed_item = schemas.ProcessedOrderItem(
                original_input=item.original_input,
                quantity=item.quantity,
                product_id=None,
                matched_name=None,
                match_score=0.0
            )
            processed_items.append(processed_item)
            all_items_high_confidence = False
            continue

        for db_product in db_products:
            # 尝试使用 token_set_ratio 考虑中英文混合及词序问题
            # score = fuzz.ratio(item.original_input.lower(), db_product.name.lower())
            score = fuzz.token_set_ratio(item.original_input.lower(), db_product.name.lower()) / 100.0

            # 如果规格存在，可以将其加入考虑，提高匹配准确性
            # 例如，可以将 original_input 与 "产品名称 (规格)" 进行匹配
            # query_string = item.original_input.lower()
            # target_string = db_product.name.lower()
            # if db_product.specification:
            #     target_string_with_spec = f"{db_product.name.lower()} ({db_product.specification.lower()})"
            #     score_with_spec = fuzz.token_set_ratio(query_string, target_string_with_spec) / 100.0
            #     score = max(score, score_with_spec) # 取两者中较高的分数

            if score > highest_score:
                highest_score = score
                best_match = db_product

        if best_match and highest_score >= MINIMUM_MATCH_THRESHOLD:
            processed_item = schemas.ProcessedOrderItem(
                original_input=item.original_input,
                quantity=item.quantity,
                product_id=best_match.product_id,
                matched_name=best_match.name,
                match_score=highest_score
            )
            if highest_score < HIGH_CONFIDENCE_THRESHOLD:
                all_items_high_confidence = False
        else:
            # 未达到最低匹配阈值或没有匹配到任何产品
            processed_item = schemas.ProcessedOrderItem(
                original_input=item.original_input,
                quantity=item.quantity,
                product_id=None,
                matched_name=None,
                match_score=highest_score # 记录实际的低分
            )
            all_items_high_confidence = False
        
        processed_items.append(processed_item)

    order_status = "completed" if all_items_high_confidence else "pending_review"

    return {
        "customer_name": order_data.customer_name,
        "order_date": order_data.order_date,
        "items": [item.model_dump() for item in processed_items], # 确保序列化
        "status": order_status
    }