import pytesseract
from PIL import Image
from pdf2image import convert_from_bytes
import io
from fastapi import HTTPEx<PERSON>, UploadFile
from typing import List

ALLOWED_MIME_TYPES = {
    "application/pdf": "pdf",
    "image/jpeg": "jpeg",
    "image/png": "png",
}

async def extract_text_from_file(file: UploadFile) -> str:
    """
    Extracts text from an uploaded file (PDF, JPEG, PNG) using OCR.
    """
    content_type = file.content_type
    if content_type not in ALLOWED_MIME_TYPES:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported file type: {content_type}. Supported types are PDF, JPEG, PNG."
        )

    file_extension = ALLOWED_MIME_TYPES[content_type]
    file_content = await file.read()

    try:
        if file_extension == "pdf":
            images = convert_from_bytes(file_content, fmt="jpeg")
            extracted_texts: List[str] = []
            for i, image_bytes_io in enumerate(images):
                try:
                    # pdf2image returns list of PIL.Image objects directly for newer versions
                    # For older versions, it might return bytes that need to be opened.
                    # Assuming it returns PIL.Image objects based on typical usage.
                    text = pytesseract.image_to_string(image_bytes_io, lang="chi_sim+eng")
                    extracted_texts.append(text)
                except Exception as e:
                    # Log error for specific page if needed
                    print(f"Error processing page {i+1} of PDF: {e}")
                    extracted_texts.append(f"[Error processing page {i+1}]")
            return "\n".join(extracted_texts)
        elif file_extension in ["jpeg", "png"]:
            image = Image.open(io.BytesIO(file_content))
            text = pytesseract.image_to_string(image, lang="chi_sim+eng")
            return text
        else:
            # This case should ideally not be reached due to the initial check,
            # but as a safeguard:
            raise HTTPException(status_code=400, detail="Internal error: Unexpected file type after validation.")

    except pytesseract.TesseractNotFoundError:
        # 在开发/测试环境中，如果没有安装 Tesseract，返回模拟数据
        print("⚠️ Tesseract 未安装，返回模拟 OCR 结果用于测试")
        return """
        Tesseract 未安装，返回模拟 OCR 结果用于测试
        订单编号: ORD-2024-001
        客户姓名: 张三
        订单日期: 2024-05-28

        订购商品:
        1. 苹果 iPhone 15 Pro - 数量: 2
        2. MacBook Air M2 - 数量: 1
        3. AirPods Pro - 数量: 1

        总金额: ¥25,999
        备注: 请尽快发货
        """
    except Exception as e:
        # Catch other potential errors during processing
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred during text extraction: {str(e)}"
        )