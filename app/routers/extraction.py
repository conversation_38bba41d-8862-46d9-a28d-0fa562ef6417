from fastapi import APIRouter, File, UploadFile, HTTPException, Body
from app.services.ocr_service import extract_text_from_file
from app.services.llm_service import extract_structured_data_from_text
from app.schemas import ExtractedOrderData, TextForExtraction # 导入新的 schema
from typing import Dict

router = APIRouter(
    prefix="/services", # 为这个 router 下的所有路径添加前缀
    tags=["Extraction Services"], # 为 API文档添加标签
)

@router.post("/extract-text", response_model=Dict[str, str]) # 路径更新为 /services/extract-text
async def extract_text_endpoint(file: UploadFile = File(...)):
    """
    Receives a file (PDF, JPEG, PNG) and extracts text content using OCR.
    """
    if not file:
        raise HTTPException(status_code=400, detail="No file provided.")

    try:
        extracted_text = await extract_text_from_file(file)
        return {"extracted_text": extracted_text}
    except HTTPException as e:
        # Re-raise HTTPException to ensure proper FastAPI error handling
        raise e
    except Exception as e:
        # Catch any other unexpected errors from the service
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")

@router.post("/extract-structured-data", response_model=ExtractedOrderData)
async def extract_structured_data_endpoint(
    payload: TextForExtraction = Body(...) # 使用 Pydantic 模型接收 JSON body
):
    """
    Receives plain text and extracts structured order information using LLM.
    Input: {"text": "OCR extracted text content..."}
    Output: Structured JSON data as defined in ExtractedOrderData schema.
    """
    if not payload.text or not payload.text.strip():
        raise HTTPException(status_code=400, detail="Input text cannot be empty.")

    try:
        structured_data = await extract_structured_data_from_text(payload.text)
        return structured_data
    except ValueError as e: # 更具体地捕获服务层可能抛出的 ValueError
        raise HTTPException(status_code=422, detail=f"Error processing text with LLM: {str(e)}")
    except ConnectionError as e: # 捕获连接错误
        raise HTTPException(status_code=503, detail=f"LLM service connection error: {str(e)}")
    except Exception as e:
        # Catch any other unexpected errors from the service or LLM call
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred during structured data extraction: {str(e)}")