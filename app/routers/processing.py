from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Any

from app import schemas
from app.services import matching_service
from app.database import get_db

router = APIRouter()

@router.post("/services/match-products", response_model=schemas.OrderProcessResponse)
async def match_products_endpoint(
    order_data: schemas.OrderProcessRequest, 
    db: Session = Depends(get_db)
) -> Any:
    """
    接收LLM提取的结构化订单数据，进行产品模糊匹配，并返回更新后的订单数据。
    """
    try:
        processed_order = matching_service.match_products_for_order(order_data, db)
        return processed_order
    except Exception as e:
        # 更具体的错误处理可以在 matching_service 中完成，这里捕获通用异常
        raise HTTPException(status_code=500, detail=f"An error occurred during product matching: {str(e)}")