from sqlalchemy import Column, Integer, String, Numeric, Index
from .database import Base

class Product(Base):
    __tablename__ = "products"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    product_code = Column(String, unique=True, index=True, nullable=False) # 品號
    name = Column(String, index=True, nullable=False) # 品名
    specification = Column(String, nullable=True) # 规格 (Excel中未直接体现，设为可空)
    unit = Column(String, nullable=False) # 單位
    price = Column(Numeric(10, 2), nullable=True) # 单价 (Excel中未直接体现，设为可空，假设10位总长，2位小数)
    currency = Column(String(3), nullable=True) # 幣別 (例如 NTD, USD)

    __table_args__ = (
        Index('ix_product_name', 'name'), # 为产品名称创建索引
    )

    def __repr__(self):
        return f"<Product(id={self.id}, product_code='{self.product_code}', name='{self.name}')>"