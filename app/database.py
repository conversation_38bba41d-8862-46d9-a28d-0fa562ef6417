from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

# 从 docker-compose.yml 中获取数据库连接信息
# 通常，这些信息会通过环境变量传入，但为了简化，我们直接使用
# docker-compose.yml 中定义的服务名 'db' 作为主机名。
# 默认 PostgreSQL 用户是 'postgres'，密码在 docker-compose.yml 中设置。
# 假设数据库名称与用户相同，或者是一个预定义的名称，例如 'app'。
# 请根据您的 docker-compose.yml 文件调整这里的用户名、密码、主机和数据库名。
# 示例：SQLALCHEMY_DATABASE_URL = "****************************************"
# 根据上一个任务的 docker-compose.yml (如果可用)，通常会是：
# POSTGRES_USER: user
# POSTGRES_PASSWORD: password
# POSTGRES_DB: invoice_agent_db (或者其他您定义的名字)
# 服务名: db
SQLALCHEMY_DATABASE_URL = "postgresql+psycopg://user:password@localhost:5433/invoice_agent_db"

engine = create_engine(SQLALCHEMY_DATABASE_URL)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

# Dependency to get DB session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()