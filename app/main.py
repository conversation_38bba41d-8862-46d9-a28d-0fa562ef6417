from fastapi import FastAPI, Depends
from sqlalchemy.orm import Session
from typing import List

from . import models, schemas
from .database import get_db # 确保 database.py 定义了 get_db
from .routers import extraction, processing

# Alembic 现在管理数据库模式，所以下面这行通常不再需要在这里调用
# models.Base.metadata.create_all(bind=engine)

app = FastAPI()
app.include_router(extraction.router)
app.include_router(processing.router)


@app.get("/")
async def read_root():
    return {"message": "Invoice Agent System - Welcome!"}


@app.get("/products/", response_model=List[schemas.ProductRead])
def read_products(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """
    Retrieve all products from the database.
    """
    products = db.query(models.Product).offset(skip).limit(limit).all()
    return products

# 后续将在此处集成 SQLAlchemy 和数据库相关逻辑
# 例如:
# from .database import engine, Base
# Base.metadata.create_all(bind=engine)