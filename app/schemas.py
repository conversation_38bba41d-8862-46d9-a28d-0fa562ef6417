from pydantic import BaseModel
from typing import Optional, Union
from decimal import Decimal

class ProductBase(BaseModel):
    product_code: str
    name: str
    unit: str
    specification: Optional[str] = None
    currency: Optional[str] = None
    price: Optional[Decimal] = None

class ProductCreate(ProductBase):
    pass

class ProductRead(ProductBase):
    id: int

    class Config:
        from_attributes = True # Pydantic V2 uses from_attributes instead of orm_mode
from typing import List

class ExtractedItem(BaseModel):
    original_input: str
    quantity: int

class ExtractedOrderData(BaseModel):
    customer_name: str
    order_date: str
    items: List[ExtractedItem]

class TextForExtraction(BaseModel):
    text: str
class OrderItemBase(BaseModel): # 将作为 OrderProcessRequest 中 items 的类型
    original_input: str
    quantity: int

class OrderProcessRequest(BaseModel):
    customer_name: str
    order_date: str # 保持字符串类型，与 ExtractedOrderData 一致
    items: List[OrderItemBase]

class ProcessedOrderItem(OrderItemBase):
    product_id: Optional[str] = None # 数据库中的 product_id 是字符串
    matched_name: Optional[str] = None
    match_score: float

class OrderProcessResponse(BaseModel):
    customer_name: str
    order_date: str
    items: List[ProcessedOrderItem]
    status: str