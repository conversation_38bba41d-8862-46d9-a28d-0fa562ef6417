# n8n 工作流程设计方案：Invoice Agent 系统

本文档详细描述了为 "Invoice Agent 系统" 设计的 n8n 自动化工作流程。该流程旨在通过调用一系列 FastAPI 后端服务，实现从接收订单文件到输出标准化订单 JSON 的自动化处理。

## 1. 目标

自动化处理上传的发票/订单文件，通过调用 FastAPI 服务进行 OCR、结构化数据提取和产品匹配，最终输出标准化的 JSON 订单信息，并根据处理状态发送通知。

## 2. 整体流程图 (Mermaid 概念)

```mermaid
graph TD
    A[Webhook: 接收文件/URL] --> B{IF: 是文件数据还是URL?};
    B -- 文件数据 --> C[HTTP Request: OCR服务 /services/extract-text];
    B -- 文件URL --> D[HTTP Request: 下载文件];
    D --> C;
    C --> E{Error Handling: OCR};
    E -- 成功 --> F[HTTP Request: LLM服务 /services/extract-structured-data];
    E -- 失败 --> G[Log Error/Notify Admin];
    F --> H{Error Handling: LLM};
    H -- 成功 --> I[HTTP Request: 产品匹配 /services/match-products];
    H -- 失败 --> G;
    I --> J{Error Handling: Product Match};
    J -- 成功 --> K[Set: 最终结果];
    J -- 失败 --> G;
    K --> L{Switch: 订单状态 status};
    L -- "pending_review" --> M[Notify: 人工审核 (Email/Slack)];
    L -- 其他状态 --> N[End: 处理完成];
    M --> N;
```

## 3. 关键 n8n 节点配置说明

以下是工作流程中关键节点的类型和主要配置建议。

### 流程变量

*   **`baseUrl`**:
    *   **描述**: FastAPI 服务的根地址。
    *   **建议值**: `http://localhost:8000` (或您的实际 FastAPI 服务地址)
    *   **用途**: 在所有 HTTP Request 节点中引用，方便统一管理和修改服务地址。

### 节点详情

1.  **A: Webhook 节点 (`Webhook`)**
    *   **Name**: `Receive Invoice File`
    *   **Authentication**: 根据安全需求配置 (例如 `Header Auth`)。
    *   **HTTP Method**: `POST`
    *   **Path**: `invoice-agent-upload` (或其他自定义的唯一路径)。
    *   **Response Mode**: `When last node finishes` (或 `Immediately`，根据需求选择)。
    *   **Options**:
        *   **Raw Body**: `True` (如果期望直接接收文件数据)。
        *   **Respond With**: `Last Node Output` (或自定义成功/错误消息)。
    *   **说明**: 此节点作为流程的起点，等待外部系统通过 POST 请求提交数据。提交的数据可能是包含文件 URL 的 JSON，也可能是 `multipart/form-data` 形式的文件。

2.  **B: IF 节点 (`IF`)**
    *   **Name**: `Check Input Type`
    *   **Conditions**:
        *   **Condition 1 (Is File URL?)**:
            *   **Value 1**: `{{ $json.body.file_url }}` (假设表单提交的 JSON 中包含 `file_url` 字段)
            *   **Operation**: `Exists`
            *   **Output**: `true` (连接到节点 D: Download File from URL)
        *   **Default Output / Condition 2 (Is Binary File?)**:
            *   **Value 1**: `{{ $binary.data }}` (或 `$node["Receive Invoice File"].binary.data`，检查 Webhook 是否直接收到了二进制数据)
            *   **Operation**: `Exists`
            *   **Output**: `false` (连接到节点 C: Call OCR Service)
    *   **说明**: 判断 Webhook 收到的是文件 URL 还是直接的文件数据，并据此决定流程走向。

3.  **D: HTTP Request 节点 (`HttpRequest`) - 下载文件**
    *   *(仅当输入为文件 URL 时执行)*
    *   **Name**: `Download File from URL`
    *   **Request Method**: `GET`
    *   **URL**: `{{ $json.body.file_url }}` (从 Webhook 节点的 JSON body 中获取 `file_url`)
    *   **Options**:
        *   **Response Format**: `File`
        *   **Store Binary Data**: `True` (确保下载的文件内容作为二进制数据传递给下一个节点)
    *   **说明**: 如果 Webhook 收到的是文件 URL，此节点负责下载文件内容。

4.  **C: HTTP Request 节点 (`HttpRequest`) - 调用 OCR 服务**
    *   **Name**: `Call OCR Service`
    *   **Request Method**: `POST`
    *   **URL**: `{{ $workflow.variables.baseUrl }}/services/extract-text`
    *   **Send Body**: `True`
    *   **Body Content Type**: `Form-Data (multipart/form-data)`
    *   **Specify Body**: `Add Field`
        *   **Name**: `file`
        *   **Value**:
            *   如果来自直接文件上传 (B 的 `false` 输出): `{{ $node["Receive Invoice File"].binary.data }}`
            *   如果来自文件下载 (D 的输出): `{{ $node["Download File from URL"].binary.data }}`
            *   *注意: 可以使用表达式或 Merge 节点来优雅地处理这两种数据来源。一个简化的表达式示例（具体需根据 IF 节点输出调整）：`{{ $items("Check Input Type")[0].json.output === 'true' ? $items("Download File from URL")[0].binary.data : $items("Receive Invoice File")[0].binary.data }}`*
        *   **Type**: `File from Variable`
    *   **Options**:
        *   **Continue on Fail**: `True` (将错误传递给后续的错误处理节点)
    *   **说明**: 将文件数据发送到 FastAPI 的 OCR 服务 (`/services/extract-text`) 以提取文本。

5.  **E, H, J: 错误处理逻辑 (Error Handling)**
    *   **实现方式**: 通常在每个关键的 HTTP Request 节点之后，通过检查该节点的输出来实现。如果节点配置了 `Continue on Fail: True`，则失败时会在其输出中包含错误信息 (例如在 `$json.error` 或通过检查 `{{ $json.statusCode }}` 是否为成功状态码)。
    *   **常用节点**: `IF` 或 `Switch` 节点。
    *   **Name Examples**: `Handle OCR Error`, `Handle LLM Error`, `Handle Match Error`
    *   **Logic**:
        *   **Condition**: `{{ $json.error }}` **Exists** (或 `{{ $json.statusCode }}` != 200/201)
        *   **True Output (Error Occurred)**: 连接到节点 G (错误处理流程)。
        *   **False Output (Success)**: 连接到下一个正常的业务处理节点。
    *   **说明**: 捕获对 FastAPI 服务的调用失败情况，并引导流程至统一的错误处理模块。

6.  **G: 错误处理流程 (Error Handling Flow)**
    *   **Name**: `Log Error and Notify Admin`
    *   **包含节点示例**:
        *   **Set Node**: 格式化错误信息，提取如错误消息、失败的节点名、执行 ID 等。
        *   **Function Node**: 执行自定义的错误记录逻辑（例如写入日志文件或数据库）。
        *   **Email/Slack/Discord Node**: 向管理员或相关团队发送错误通知。
        *   **Stop Workflow Node (Optional)**: 根据错误严重性决定是否终止整个工作流程。
    *   **说明**: 定义当 API 调用失败时的具体响应操作，如记录日志、发送警报等。

7.  **F: HTTP Request 节点 (`HttpRequest`) - 调用 LLM 结构化提取服务**
    *   **Name**: `Call LLM Service`
    *   **Request Method**: `POST`
    *   **URL**: `{{ $workflow.variables.baseUrl }}/services/extract-structured-data`
    *   **Send Body**: `True`
    *   **Body Content Type**: `JSON`
    *   **JSON Parameters**: `True`
    *   **Body**: `{"text": "{{ $node["Call OCR Service"].json.extracted_text }}"}` (从 OCR 服务节点的输出中获取 `extracted_text` 字段的值)
    *   **Options**:
        *   **Continue on Fail**: `True`
    *   **说明**: 将 OCR 提取的文本发送到 FastAPI 的 LLM 服务 (`/services/extract-structured-data`) 进行结构化数据提取。

8.  **I: HTTP Request 节点 (`HttpRequest`) - 调用产品模糊匹配服务**
    *   **Name**: `Call Product Matching Service`
    *   **Request Method**: `POST`
    *   **URL**: `{{ $workflow.variables.baseUrl }}/services/match-products`
    *   **Send Body**: `True`
    *   **Body Content Type**: `JSON`
    *   **JSON Parameters**: `True`
    *   **Body**: `{{ JSON.stringify($node["Call LLM Service"].json) }}` (直接使用 LLM 服务节点的完整 JSON 输出作为请求体，因为其结构与产品匹配服务期望的输入 `OrderProcessRequest` 兼容)
    *   **Options**:
        *   **Continue on Fail**: `True`
    *   **说明**: 将 LLM 提取的结构化订单数据发送到 FastAPI 的产品匹配服务 (`/services/match-products`)。

9.  **K: Set 节点 (`Set`) - 设置最终结果**
    *   **Name**: `Set Final Order Data`
    *   **Keep Only Set**: `True` (可选，用于清理数据流，只保留此节点设置的字段)。
    *   **Values to Set**:
        *   **Name**: `final_order` (或其他描述性名称)
        *   **Value**: `{{ $node["Call Product Matching Service"].json }}`
    *   **说明**: 将产品匹配服务返回的最终订单 JSON 设置为一个易于引用的字段，方便后续节点（如 Switch 和通知节点）使用。

10. **L: Switch 节点 (`Switch`) - 根据订单状态分流**
    *   **Name**: `Check Order Status`
    *   **Input Data Field**: `{{ $json.final_order.status }}` (从 "Set Final Order Data" 节点获取 `status` 字段)
    *   **Routing Rules**:
        *   **Rule 1**:
            *   **Operation**: `Equals`
            *   **Value**: `pending_review`
            *   **Output**: `0` (连接到节点 M: Notify for Manual Review)
        *   **Default Output / Rule 2 (或其他状态，如 "completed")**:
            *   **Output**: `1` (连接到节点 N: End Workflow)
    *   **说明**: 根据最终订单的 `status` 字段决定流程的下一步，特别是当订单需要人工审核时。

11. **M: 通知节点 (例如 `EmailNode`, `SlackNode`) - 人工审核通知**
    *   **Name**: `Notify for Manual Review`
    *   **Configuration**: 根据选择的通知服务进行具体配置。
        *   **To (Email)**: `<EMAIL>` (替换为实际邮箱)
        *   **Subject (Email)**: `订单需要人工审核: {{ $json.final_order.customer_name }}`
        *   **Message (Email/Slack)** (示例):
            ```
            一个新的订单需要人工审核。
            客户名称: {{ $json.final_order.customer_name }}
            订单日期: {{ $json.final_order.order_date }}
            当前状态: {{ $json.final_order.status }}
            订单项目:
            {{ $json.final_order.items.map(item => `- ${item.original_input} (数量: ${item.quantity}) | 匹配产品: ${item.matched_name || '未匹配'} | 分数: ${item.match_score}`).join('\\n') }}

            n8n 执行链接: {{ $execution.url }}
            ```
    *   **说明**: 当订单状态为 `pending_review` 时，向相关人员发送通知，并提供订单的关键信息和 n8n 执行链接以便快速定位。

12. **N: NoOp 节点 (`NoOp`) 或结束节点**
    *   **Name**: `End Workflow` / `Processing Complete`
    *   **说明**: 标记工作流程的正常结束点。对于某些分支，这可能是一个明确的结束标志。

## 4. 数据转换/处理

*   **Webhook (文件 URL) -> OCR 服务**:
    *   如果 Webhook 接收的是文件 URL，需要一个额外的 HTTP Request 节点（节点 D）来下载文件内容，然后将二进制数据传递给 OCR 服务节点。
*   **OCR 服务 -> LLM 服务**:
    *   OCR 服务输出: `{"extracted_text": "..."}`
    *   LLM 服务输入: `{"text": "..."}`
    *   n8n 中，LLM 节点的 Body 可以配置为: `{"text": "{{ $node["Call OCR Service"].json.extracted_text }}"}`。
*   **LLM 服务 -> 产品匹配服务**:
    *   LLM 服务输出 (`ExtractedOrderData`) 的 JSON 结构与产品匹配服务输入 (`OrderProcessRequest`) 的结构基本一致。
    *   可以直接将 LLM 服务节点的 JSON 输出 (`{{ JSON.stringify($node["Call LLM Service"].json) }}`) 作为产品匹配服务节点的 Body。

## 5. 最终输出

*   最终标准化的 JSON 订单信息是**产品匹配服务 (`/services/match-products`)** 的输出。
*   在 n8n 中，这个数据可以在 "Call Product Matching Service" 节点之后，通过表达式 `{{ $node["Call Product Matching Service"].json }}` 获取。
*   如果使用了 "Set Final Order Data" 节点 (K)，则可以通过 `{{ $json.final_order }}` 获取。
*   n8n 的执行日志会清晰显示每个节点的输入和输出，方便调试和查看最终结果。

## 6. 关于价格计算的说明

根据当前讨论，**暂时不在此 n8n 工作流程中实现价格和订单合计的计算**。最终输出的 JSON 将不包含每个订单项的单价、总价或订单总金额，除非这些信息由您的 `/services/match-products` FastAPI 服务直接返回。如果未来需要加入价格计算，可以考虑更新 FastAPI 服务或在 n8n 中增加额外的处理步骤。

## 7. 概念性的 n8n 工作流 JSON 结构 (片段)

以下是一个高度简化的 n8n 工作流 JSON 结构片段，用于展示节点和连接的基本逻辑概念。实际的 n8n 工作流 JSON 会更复杂，包含更多细节和元数据。

```json
{
  "name": "Invoice Agent Processing Workflow",
  "nodes": [
    {
      "parameters": {
        "path": "invoice-agent-upload",
        "httpMethod": "POST",
        "options": {}
      },
      "name": "Receive Invoice File",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1, // 版本号可能因 n8n 版本而异
      "position": [450, 300]
    },
    // ... 其他节点定义，例如 IF, HTTP Request, Set, Switch ...
    {
      "parameters": {
        "values": {
          "string": [
            { "name": "final_order", "value": "={{ $items('Call Product Matching Service')[0].json }}" }
          ]
        },
        "keepOnlySet": true
      },
      "name": "Set Final Order Data",
      "type": "n8n-nodes-base.set",
      "typeVersion": 1,
      "position": [1450, 300]
    }
  ],
  "connections": {
    "Receive Invoice File": {
      "main": [ [ { "node": "Check Input Type", "type": "main", "index": 0 } ] ]
    }
    // ... 其他连接定义 ...
  },
  "settings": {},
  "variables": [
    { "name": "baseUrl", "value": "http://localhost:8000" }
  ]
}
```
**免责声明**: 上述 JSON 片段仅为示意，并非可直接导入的完整工作流。

---

本文档旨在为您提供一个清晰的 n8n 工作流程设计蓝图。在实际实施过程中，可能需要根据 n8n 的具体版本和操作习惯进行微调。