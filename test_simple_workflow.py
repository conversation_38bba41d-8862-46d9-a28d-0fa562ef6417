#!/usr/bin/env python3
"""
测试简化的 n8n 工作流
"""
import requests
import os

def test_simple_workflow():
    """测试简化的 n8n 工作流"""
    webhook_url = "http://localhost:5678/webhook/simple-test"
    image_path = "/Users/<USER>/code/jiangying000/n8n-proj-1_副本/data/output-001.jpeg"
    
    print("🔗 测试简化的 n8n Webhook...")
    
    if not os.path.exists(image_path):
        print(f"❌ 测试图片不存在: {image_path}")
        return False
    
    try:
        with open(image_path, 'rb') as f:
            files = {'file': ('output-001.jpeg', f, 'image/jpeg')}
            response = requests.post(webhook_url, files=files)
            print(f"✅ Webhook 状态码: {response.status_code}")
            print(f"✅ 响应内容: {response.text}")
            
            if response.status_code == 200:
                return True
            else:
                return False
    except Exception as e:
        print(f"❌ Webhook 失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试简化工作流...")
    test_simple_workflow()
    print("✨ 测试完成!")
