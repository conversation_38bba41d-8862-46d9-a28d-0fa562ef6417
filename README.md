# Invoice Agent 系统

## 项目概览

Invoice Agent 系统旨在自动化处理订单和发票的流程。它通过结合光学字符识别 (OCR)、大型语言模型 (LLM) 和模糊匹配技术，从图片或 PDF 格式的订单文件中提取信息，并将其结构化为 JSON 数据。该系统还集成了 n8n 工作流程，以实现更灵活的数据处理和通知。

## 技术栈

*   **后端**: Python, FastAPI
*   **数据库**: PostgreSQL
*   **容器化**: Docker, Docker Compose
*   **工作流程自动化**: n8n
*   **OCR**: Tesseract OCR
*   **LLM**: OpenAI GPT 系列模型
*   **模糊匹配**: TheFuzz (FuzzyWuzzy)
*   **依赖管理**: uv
*   **数据库迁移**: Alembic

## 架构图

```mermaid
graph LR
    A[用户/客户端] -- 上传订单图片/PDF --> B(n8n Webhook)
    B -- 触发工作流 --> C{n8n 工作流}
    C -- 调用OCR服务 --> D[FastAPI: /services/extract-text]
    D -- 返回文本 --> C
    C -- 调用LLM服务 --> E[FastAPI: /services/extract-structured-data]
    E -- 返回结构化数据 --> C
    C -- 调用产品匹配服务 --> F[FastAPI: /services/match-products]
    F -- 访问 --> G[(PostgreSQL 数据库)]
    F -- 返回匹配结果 --> C
    C -- 输出/通知 --> H{最终JSON输出/邮件/Slack等}

    D -- 依赖 --> I[Tesseract OCR]
    E -- 依赖 --> J[OpenAI LLM]
    F -- 依赖 --> K[TheFuzz]
```

*(这是一个简化的文本表示架构图。)*

## 后端 FastAPI 应用

### 1. 安装步骤

**a. 创建虚拟环境**

推荐使用 `uv` 来管理 Python 环境和依赖。

```bash
uv venv
source .venv/bin/activate  # 激活虚拟环境 (Linux/macOS)
# .\.venv\Scripts\activate # 激活虚拟环境 (Windows)
```

**b. 安装依赖**

使用 `uv` 安装项目依赖：

```bash
uv pip sync pyproject.toml
```

或者，如果您想要安装开发依赖：

```bash
uv pip install -e ".[dev]"
```

### 2. 数据库设置

**a. 启动 PostgreSQL 数据库**

项目使用 Docker Compose 来管理 PostgreSQL 服务。

```bash
docker-compose up -d postgres
```
*(确保 Docker 和 Docker Compose 已安装并正在运行)*

**b. 运行 Alembic 数据库迁移**

Alembic 用于管理数据库模式的迁移。

```bash
uv run alembic upgrade head
```
*(确保数据库服务已启动)*

或者，如果您已经激活了虚拟环境：

```bash
alembic upgrade head
```

### 3. 加载初始产品数据

系统需要一个产品列表来进行订单项的匹配。

**a. 准备产品数据文件**

将包含产品信息的 Excel 文件 `產品清單客戶訂單資料.xlsx` 放入项目根目录下的 `data/` 文件夹中。如果 `data/` 目录不存在，请创建它。

**b. 运行加载脚本**

```bash
uv run python scripts/load_products.py
```
*(确保数据库服务已启动且迁移已完成)*

或者，如果您已经激活了虚拟环境：

```bash
python scripts/load_products.py
```

### 4. 环境变量配置

创建一个 `.env` 文件在项目的根目录下，并根据 `.env.example` (如果提供) 或以下指导设置必要的环境变量：

```env
# .env 文件示例
DATABASE_URL="postgresql+psycopg://user:password@localhost:5433/invoice_agent_db" # 根据 docker-compose.yml 和 alembic.ini 中的配置修改 (主机端口为5433)
OPENAI_API_KEY="sk-YOUR_OPENAI_API_KEY"
# 如果需要通过代理访问 OpenAI API
# HTTP_PROXY="http://localhost:1080"
# HTTPS_PROXY="http://localhost:1080"

# n8n Webhook URL (用于 FastAPI 向 n8n 发送回调，如果需要)
# N8N_WEBHOOK_URL="YOUR_N8N_CALLBACK_WEBHOOK_URL"

# FastAPI 应用的基本 URL (用于 n8n 调用 FastAPI)
FASTAPI_BASE_URL="http://localhost:8000"
```

**重要**:
*   将 `user`, `password`, `localhost`, `5432`, `invoice_agent_db` 替换为您的实际 PostgreSQL 配置 (通常与 [`docker-compose.yml`](docker-compose.yml:1) 中的设置一致)。
*   将 `sk-YOUR_OPENAI_API_KEY` 替换为您的真实 OpenAI API 密钥。
*   如果您的网络环境需要代理才能访问 OpenAI API，请取消注释并设置 `HTTP_PROXY` 和 `HTTPS_PROXY` 为 `http://localhost:1080` (根据用户偏好)。

### 5. 运行 FastAPI 应用

```bash
uvicorn app.main:app --reload
```

或者，如果您在 `pyproject.toml` 中定义了脚本，可以使用：
```bash
uv run uvicorn app.main:app --reload
```

应用默认会在 `http://localhost:8000` 启动。

### 6. API 端点列表

*   **`GET /`**:
    *   描述: 系统欢迎接口。
    *   示例输出: `{"message": "Invoice Agent System - Welcome!"}`
*   **`GET /products/`**:
    *   描述: 获取数据库中存储的所有产品列表。
    *   参数: `skip` (int, optional), `limit` (int, optional)
    *   示例输出: `[{"id": 1, "name": "Product A", "category": "Category X", ...}, ...]`
*   **`POST /services/extract-text`**:
    *   描述: 从上传的文件 (PDF, JPEG, PNG) 中提取文本内容 (OCR)。
    *   请求体: `file` (UploadFile)
    *   示例输出: `{"extracted_text": "提取到的文本内容..."}`
*   **`POST /services/extract-structured-data`**:
    *   描述: 从纯文本中提取结构化的订单信息 (LLM)。
    *   请求体: `{"text": "OCR 提取到的文本内容..."}` (符合 `TextForExtraction` schema)
    *   示例输出: (符合 `ExtractedOrderData` schema)
        ```json
        {
          "order_id": "ORD12345",
          "customer_name": "张三",
          "order_date": "2024-05-28",
          "items": [
            {"item_description": "产品A", "quantity": 2, "unit_price": 100.0},
            {"item_description": "产品B", "quantity": 1, "unit_price": 50.0}
          ],
          "total_amount": 250.0
        }
        ```
*   **`POST /services/match-products`**:
    *   描述: 接收 LLM 提取的结构化订单数据，进行产品模糊匹配，并返回更新后的订单数据。
    *   请求体: (符合 `OrderProcessRequest` schema, 通常是 `/services/extract-structured-data` 的输出)
    *   示例输出: (符合 `OrderProcessResponse` schema)
        ```json
        {
          "order_id": "ORD12345",
          "customer_name": "张三",
          "order_date": "2024-05-28",
          "items": [
            {
              "item_description_original": "产品A",
              "quantity": 2,
              "unit_price": 100.0,
              "matched_product_id": 1,
              "matched_product_name": "标准产品A型",
              "match_score": 95.5,
              "needs_review": false
            },
            // ... 其他产品项
          ],
          "total_amount": 250.0,
          "all_items_matched_confidently": true,
          "processing_summary": "所有项目高置信度匹配。"
        }
        ```

## n8n 工作流程

### 1. 导入工作流程

a.  打开您的 n8n 实例。
b.  在左侧导航栏选择 "Workflows"。
c.  点击 "Import from File" 或 "Import from URL"。
d.  如果使用文件，您可以从项目中的 `n8n_workflow/invoice_agent_workflow.n8n.json` 路径下找到此文件。选择此文件进行导入。
e.  点击 "Import"。

### 2. n8n JSON 配置文件

n8n 工作流程的 JSON 配置文件名为 `invoice_agent_workflow.n8n.json`，位于项目的 `n8n_workflow/` 目录下。

您可以从 `n8n_workflow/invoice_agent_workflow.n8n.json` 路径下找到此文件。

### 3. 启动 n8n 服务

n8n 服务已经集成到项目的 `docker-compose.yml` 文件中。

**启动所有服务（包括 n8n）**:

```bash
docker-compose up -d
```

**只启动 n8n 服务**:

```bash
docker-compose up -d n8n
```

**访问 n8n 界面**:

服务启动后，您可以通过浏览器访问 `http://localhost:5678` 来打开 n8n 用户界面。

**默认登录凭证**:
- 用户名: `admin`
- 密码: `password`

**注意**: 在生产环境中，请修改 `docker-compose.yml` 中的默认密码。

### 4. 必要配置

导入工作流程后，您可能需要配置以下节点：

*   **Webhook 节点**:
    *   **Webhook URL**: n8n 会为 Webhook 节点生成一个唯一的 URL (包括测试 URL 和生产 URL)。您需要复制此 URL，后续将用它来触发工作流程。
    *   **HTTP Method**: 通常设置为 `POST`。
    *   **Path**: Webhook 节点路径，例如 `invoice-agent-webhook`。
    *   **Authentication**: 根据需要配置，例如 "Header Auth" 并设置一个安全的 Token。
*   **HTTP Request 节点 (用于调用 FastAPI)**:
    *   **Base URL**: 设置为您的 FastAPI 应用的地址，例如 `http://host.docker.internal:8000` (如果 n8n 和 FastAPI 都在 Docker 中运行，且在同一网络) 或 `http://localhost:8000` (如果 n8n 可以直接访问 FastAPI)。请参考之前设置的 `FASTAPI_BASE_URL` 环境变量。
    *   **URL**: 具体的 API 端点路径，例如 `/services/extract-text`, `/services/extract-structured-data`, `/services/match-products`。
    *   **Authentication**: 如果 FastAPI 端点需要认证，请在此配置。
    *   **Body Content Type**: 通常是 `JSON`。
    *   **Specify Body**: 根据 API 要求构造请求体，通常使用 n8n 的表达式来传递前序节点的数据。
*   **凭证配置 (Credentials)**:
    *   **OpenAI**: 如果 n8n 工作流中直接调用 OpenAI (而不是通过 FastAPI)，您需要在 n8n 中配置 OpenAI 凭证。
    *   **Email/Slack/Other Notification Services**: 如果工作流包含发送邮件或 Slack 消息的节点，您需要在 n8n 中为这些服务配置相应的凭证。

### 3. 如何触发工作流程

**a. 使用 `curl` (示例)**

假设您的 n8n Webhook URL 是 `https://your-n8n-instance.com/webhook/your-webhook-path`，并且它接受 `POST` 请求，文件字段名为 `file`:

```bash
curl -X POST \
     -F "file=@/path/to/your/order_document.pdf" \
     https://your-n8n-instance.com/webhook/your-webhook-path
```
*(将 `/path/to/your/order_document.pdf` 替换为实际的文件路径，并将 URL 替换为您的 Webhook URL)*

**b. 使用简单的 HTML 表单 (示例)**

创建一个简单的 HTML 文件：

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Upload Order</title>
</head>
<body>
    <h1>Upload Order Document</h1>
    <form action="https://your-n8n-instance.com/webhook/your-webhook-path" method="post" enctype="multipart/form-data">
        <input type="file" name="file" required>
        <button type="submit">Upload</button>
    </form>
</body>
</html>
```
*(将 `https://your-n8n-instance.com/webhook/your-webhook-path` 替换为您的 Webhook URL)*

在浏览器中打开此 HTML 文件，选择文件并提交即可触发工作流程。

### 5. 工作流程图解

本项目的 n8n 工作流程设计详情，请参考文档: [`n8n_workflow_design.md`](n8n_workflow_design.md:1)。

该文档包含了详细的流程图和每个关键节点的说明，解释了数据如何在不同服务（OCR, LLM, 产品匹配）之间流转，以及如何处理分支逻辑和错误。

**核心流程概述**:

1.  **Webhook 触发**: 接收到上传的订单文件 (图片/PDF)。
2.  **OCR 提取文本**: 调用 FastAPI 的 `/services/extract-text` 端点，将文件发送给 OCR 服务，获取纯文本内容。
3.  **LLM 提取结构化数据**: 调用 FastAPI 的 `/services/extract-structured-data` 端点，将 OCR 提取的文本发送给 LLM 服务，获取结构化的订单信息 (如订单号、客户名、产品列表等)。
4.  **产品模糊匹配**: 调用 FastAPI 的 `/services/match-products` 端点，将结构化的订单数据（特别是产品列表）发送给产品匹配服务，与数据库中的产品进行模糊匹配。
5.  **结果处理与输出**:
    *   根据匹配结果，可能进行人工审核标记。
    *   生成最终的 JSON 输出。
    *   （可选）通过邮件、Slack 或其他方式发送通知或处理结果。

## 测试整个流程 (端到端示例)

1.  **确保所有服务已启动**:
    *   启动所有服务: `docker-compose up -d`
    *   或者分别启动:
        *   PostgreSQL 数据库: `docker-compose up -d db`
        *   n8n 服务: `docker-compose up -d n8n`
    *   FastAPI 应用 (`uvicorn app.main:app --reload` 或 `uv run uvicorn app.main:app --reload`)。
    *   确保 n8n 工作流程已导入并激活。
2.  **准备测试文件**: 准备一个包含订单信息的 PDF 或图片文件 (例如 `sample_order.pdf`)。
3.  **触发 n8n Webhook**:
    *   使用 `curl` 命令 (如上文示例) 或 HTML 表单上传 `sample_order.pdf` 到 n8n Webhook URL。
4.  **观察 n8n 流程执行**:
    *   在 n8n 的 "Executions" 标签页中，您应该能看到一个新的工作流实例开始执行。
    *   可以点击该实例查看每个节点的输入和输出数据，跟踪流程的进展。
    *   检查 FastAPI 应用的控制台日志，确认 API 端点被 n8n 成功调用。
5.  **获取最终 JSON 输出**:
    *   工作流程的最后一个节点通常会输出最终处理完成的 JSON 数据。您可以在 n8n 的执行详情中查看此输出。
    *   如果配置了通知，您应该会收到相应的邮件或消息。

## 示例输出 (JSON)

以下是一个符合最终要求的完整 JSON 输出示例：

```json
{
  "order_id": "INV2024-001",
  "customer_name": "示例客户公司",
  "order_date": "2024-05-29",
  "invoice_upload_timestamp": "2024-05-29T10:30:00Z",
  "processing_status": "Completed",
  "items": [
    {
      "item_description_original": "高级办公椅 Ergonomic Chair Model X",
      "quantity": 2,
      "unit_price_original": 350.00,
      "line_total_original": 700.00,
      "matched_product_id": "PROD001",
      "matched_product_name": "高级人体工学办公椅 - 型号X",
      "matched_product_category": "办公家具",
      "matched_unit_price": 349.99,
      "match_score": 92.5,
      "needs_review": false,
      "review_notes": null
    },
    {
      "item_description_original": "无线键盘鼠标套装",
      "quantity": 5,
      "unit_price_original": 79.90,
      "line_total_original": 399.50,
      "matched_product_id": "PROD015",
      "matched_product_name": "无线键鼠套装 - 黑色",
      "matched_product_category": "电脑配件",
      "matched_unit_price": 79.00,
      "match_score": 88.0,
      "needs_review": true,
      "review_notes": "价格略有差异，请确认。"
    },
    {
      "item_description_original": "大容量移动硬盘 2TB",
      "quantity": 1,
      "unit_price_original": 120.00,
      "line_total_original": 120.00,
      "matched_product_id": null,
      "matched_product_name": null,
      "matched_product_category": null,
      "matched_unit_price": null,
      "match_score": 45.0,
      "needs_review": true,
      "review_notes": "未找到匹配产品，或匹配度过低。"
    }
  ],
  "subtotal_original": 1219.50,
  "subtotal_matched": 1098.98,
  "total_amount_original": 1219.50,
  "total_amount_matched": 1098.98,
  "currency": "CNY",
  "all_items_matched_confidently": false,
  "processing_summary": "部分项目需要人工审核。2个项目高置信度匹配，1个项目低置信度或未匹配。",
  "ocr_raw_text": "这里是OCR提取的原始文本...",
  "llm_raw_output": {
    "order_id": "INV2024-001",
    "customer_name": "示例客户公司",
    "order_date": "2024-05-29",
    "items": [
        {"item_description": "高级办公椅 Ergonomic Chair Model X", "quantity": 2, "unit_price": 350.00},
        {"item_description": "无线键盘鼠标套装", "quantity": 5, "unit_price": 79.90},
        {"item_description": "大容量移动硬盘 2TB", "quantity": 1, "unit_price": 120.00}
    ],
    "total_amount": 1219.50
  }
}
```

## 错误处理和 Edge Cases

*   **OCR 失败**:
    *   如果无法从文件中提取文本 (例如，图片质量极差、非支持格式)，FastAPI 的 `/services/extract-text` 端点会返回错误。
    *   n8n 工作流应捕获此错误，并可能将任务标记为失败或通知人工处理。
*   **LLM 解析问题**:
    *   如果 LLM 无法从文本中提取预期的结构化数据，或者返回的数据格式不正确，FastAPI 的 `/services/extract-structured-data` 端点会返回错误。
    *   n8n 工作流应捕获此错误，记录问题，并可能通知人工介入。
*   **低匹配度**:
    *   如果产品匹配服务的匹配分数低于预设阈值，`OrderProcessResponse` 中的 `needs_review` 字段会标记为 `true`。
    *   n8n 工作流可以根据此标记将订单路由到人工审核队列。
*   **空订单/无产品项**:
    *   LLM 服务应能处理没有产品项的订单文本，返回空的 `items` 列表。
    *   产品匹配服务在接收到空 `items` 列表时，应正常处理并返回。
*   **无法识别的产品**:
    *   如果订单中的某个产品在数据库中完全找不到匹配项，或者最佳匹配的得分非常低，该产品项在 `OrderProcessResponse` 中 `matched_product_id` 可能为 `null`，并且 `needs_review` 为 `true`。
*   **API 密钥或服务不可用**:
    *   如果 OpenAI API 密钥无效或服务暂时不可用，FastAPI 服务会返回相应的 HTTP 错误 (例如 401, 503)。
    *   n8n 工作流应配置重试机制或错误处理流程。
*   **数据库连接问题**:
    *   如果 FastAPI 应用无法连接到 PostgreSQL 数据库，相关端点会失败。确保数据库服务正常运行且网络可达。

系统设计上，n8n 工作流是错误处理和流程控制的核心。通过在 n8n 中设置错误触发器 (Error Triggers) 和条件逻辑，可以更优雅地处理各种异常情况。

---

*后续将添加更多关于部署、维护和未来可能扩展的说明。*