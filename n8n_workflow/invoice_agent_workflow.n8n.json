{"name": "Invoice Agent Processing Workflow", "nodes": [{"parameters": {"path": "invoice-agent-upload", "httpMethod": "POST", "options": {}}, "name": "Receive Invoice File", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [200, 300], "webhookId": "invoice-agent-upload"}, {"parameters": {"url": "http://host.docker.internal:8000/services/extract-text", "method": "POST", "requestMethod": "POST", "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "file", "value": "", "parameterType": "formBinaryData", "inputDataFieldName": "file"}]}, "options": {}}, "typeVersion": 3, "name": "Call OCR Service", "type": "n8n-nodes-base.httpRequest", "position": [400, 300]}, {"parameters": {"url": "http://host.docker.internal:8000/services/extract-structured-data", "method": "POST", "requestMethod": "POST", "sendBody": true, "contentType": "json", "bodyParameters": {"parameters": [{"name": "text", "value": "={{ $json.extracted_text }}"}]}, "options": {}}, "typeVersion": 3, "name": "Call LLM Service", "type": "n8n-nodes-base.httpRequest", "position": [600, 300]}, {"parameters": {"url": "http://host.docker.internal:8000/services/match-products", "method": "POST", "requestMethod": "POST", "jsonParameters": true, "sendBody": true, "contentType": "json", "jsonBody": "={{ JSON.stringify($json) }}", "options": {}}, "typeVersion": 3, "name": "Call Product Matching Service", "type": "n8n-nodes-base.httpRequest", "position": [800, 300]}, {"parameters": {"values": {"string": [{"name": "final_order", "value": "={{ JSON.stringify($json) }}"}, {"name": "processing_status", "value": "completed"}, {"name": "timestamp", "value": "={{ new Date().toISOString() }}"}]}, "keepOnlySet": true}, "name": "Set Final Order Data", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [1000, 300]}], "connections": {"Receive Invoice File": {"main": [[{"node": "Call OCR Service", "type": "main", "index": 0}]]}, "Call OCR Service": {"main": [[{"node": "Call LLM Service", "type": "main", "index": 0}]]}, "Call LLM Service": {"main": [[{"node": "Call Product Matching Service", "type": "main", "index": 0}]]}, "Call Product Matching Service": {"main": [[{"node": "Set Final Order Data", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 0, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1", "variables": [{"name": "baseUrl", "value": "http://host.docker.internal:8000"}]}