<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Agent 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="file"] {
            width: 100%;
            padding: 10px;
            border: 2px dashed #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background-color: #0056b3;
        }
        .note {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Invoice Agent 文件上传测试</h1>
        
        <form action="http://localhost:5678/webhook/invoice-agent-upload" method="post" enctype="multipart/form-data">
            <div class="form-group">
                <label for="file">选择订单文件 (PDF, JPEG, PNG):</label>
                <input type="file" name="file" id="file" accept=".pdf,.jpg,.jpeg,.png" required>
            </div>
            
            <button type="submit">上传并处理</button>
        </form>
        
        <div class="note">
            <strong>注意:</strong>
            <ul>
                <li>确保所有服务都在运行（数据库、n8n、FastAPI）</li>
                <li>确保 n8n 工作流已激活</li>
                <li>支持的文件格式：PDF, JPEG, PNG</li>
                <li>处理完成后，可以在 n8n 的 Executions 页面查看结果</li>
            </ul>
        </div>
    </div>
</body>
</html>
