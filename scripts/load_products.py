import os
import sys
import openpyxl
from sqlalchemy.orm import Session

# 将项目根目录添加到 sys.path，以便可以导入 app 模块
# 假设此脚本在 scripts/ 目录下，项目根目录是其父目录
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from app.database import SessionLocal, engine
from app.models import Product, Base # Base 用于确保表已创建（尽管 Alembic 已处理）

# 定义 Excel 文件路径
# 请确保此文件存在于 data/ 目录下
EXCEL_FILE_PATH = os.path.join(project_root, "data", "产品清单.xlsx")

def load_products_from_excel(db: Session):
    """
    从 Excel 文件加载产品数据到数据库。
    如果产品代码已存在，则更新产品信息；否则，创建新产品。
    """
    if not os.path.exists(EXCEL_FILE_PATH):
        print(f"错误: Excel 文件未找到于 {EXCEL_FILE_PATH}")
        return

    workbook = openpyxl.load_workbook(EXCEL_FILE_PATH)
    sheet = workbook.active  # 假设数据在第一个工作表中

    header = [cell.value for cell in sheet[1]]
    print(f"Excel 表头: {header}")

    # 预期列名映射 (根据用户提供的示例)
    # 品號, 品名, 單位, 幣別
    try:
        product_code_col_idx = header.index("品號") + 1
        name_col_idx = header.index("品名") + 1
        unit_col_idx = header.index("單位") + 1
        currency_col_idx = header.index("幣別") + 1
    except ValueError as e:
        print(f"错误: Excel 表头不符合预期。缺失的列: {e}")
        print("预期的列名应包含: '品號', '品名', '單位', '幣別'")
        return

    loaded_count = 0
    updated_count = 0
    skipped_count = 0

    # 从第二行开始遍历数据 (跳过表头)
    for row_num in range(2, sheet.max_row + 1):
        row_values = [cell.value for cell in sheet[row_num]]

        product_code = sheet.cell(row=row_num, column=product_code_col_idx).value
        name = sheet.cell(row=row_num, column=name_col_idx).value
        unit = sheet.cell(row=row_num, column=unit_col_idx).value
        currency = sheet.cell(row=row_num, column=currency_col_idx).value

        # 基本的数据清洗和校验
        if not product_code:
            print(f"第 {row_num} 行缺少产品代码，已跳过。")
            skipped_count += 1
            continue
        
        product_code = str(product_code).strip()
        name = str(name).strip() if name else None
        unit = str(unit).strip() if unit else None
        currency = str(currency).strip() if currency else None


        # 检查产品是否已存在
        existing_product = db.query(Product).filter(Product.product_code == product_code).first()

        if existing_product:
            # 更新现有产品
            existing_product.name = name
            existing_product.unit = unit
            existing_product.currency = currency
            # specification 和 price 在此 Excel 中未提供，如果模型中有默认值或需要更新，需相应处理
            # existing_product.specification = ...
            # existing_product.price = ... 
            updated_count += 1
            print(f"更新产品: {product_code} - {name}")
        else:
            # 创建新产品
            new_product = Product(
                product_code=product_code,
                name=name,
                unit=unit,
                currency=currency
                # specification 和 price 在此 Excel 中未提供
            )
            db.add(new_product)
            loaded_count += 1
            print(f"新增产品: {product_code} - {name}")

    try:
        db.commit()
        print(f"\n数据加载完成。")
        print(f"新增产品数量: {loaded_count}")
        print(f"更新产品数量: {updated_count}")
        print(f"跳过产品数量: {skipped_count}")
    except Exception as e:
        db.rollback()
        print(f"数据库提交失败: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    print("开始加载产品数据...")
    # 可选：如果需要，可以在这里确保表已创建，但 Alembic 应该已经处理了
    # Base.metadata.create_all(bind=engine) 
    
    db_session = SessionLocal()
    load_products_from_excel(db_session)
    print("产品数据加载脚本执行完毕。")