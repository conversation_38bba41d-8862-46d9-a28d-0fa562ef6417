#!/usr/bin/env python3
"""
测试 FastAPI 端点的脚本
"""
import requests
import sys
import os

def test_fastapi_endpoints():
    """测试 FastAPI 的各个端点"""
    base_url = "http://localhost:8000"

    print("🔍 测试 FastAPI 端点...")

    # 1. 测试根端点
    try:
        response = requests.get(f"{base_url}/")
        print(f"✅ 根端点: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ 根端点失败: {e}")
        return False

    # 2. 测试健康检查
    try:
        response = requests.get(f"{base_url}/health")
        print(f"✅ 健康检查: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"⚠️  健康检查: {e}")

    # 3. 测试文件上传端点（使用测试图片）
    image_path = "/Users/<USER>/code/jiangying000/n8n-proj-1_副本/data/output-001.jpeg"

    if not os.path.exists(image_path):
        print(f"❌ 测试图片不存在: {image_path}")
        return False

    try:
        with open(image_path, 'rb') as f:
            files = {'file': ('output-001.jpeg', f, 'image/jpeg')}
            response = requests.post(f"{base_url}/services/extract-text", files=files)
            print(f"✅ OCR 端点: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                print(f"   提取的文本长度: {len(result.get('extracted_text', ''))}")
                return True
            else:
                print(f"   错误: {response.text}")
                return False
    except Exception as e:
        print(f"❌ OCR 端点失败: {e}")
        return False

def test_webhook():
    """测试 n8n webhook"""
    webhook_url = "http://localhost:5678/webhook/invoice-agent-upload"
    image_path = "/Users/<USER>/code/jiangying000/n8n-proj-1_副本/data/output-001.jpeg"

    print("\n🔗 测试 n8n Webhook...")

    if not os.path.exists(image_path):
        print(f"❌ 测试图片不存在: {image_path}")
        return False

    try:
        with open(image_path, 'rb') as f:
            files = {'file': ('output-001.jpeg', f, 'image/jpeg')}
            response = requests.post(webhook_url, files=files)
            print(f"✅ Webhook: {response.status_code}")
            if response.status_code == 200:
                print(f"   响应: {response.text[:200]}...")
                return True
            else:
                print(f"   错误: {response.text}")
                return False
    except Exception as e:
        print(f"❌ Webhook 失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试...")

    # 测试 FastAPI
    fastapi_ok = test_fastapi_endpoints()

    if fastapi_ok:
        # 测试 Webhook
        test_webhook()
    else:
        print("\n❌ FastAPI 测试失败，跳过 Webhook 测试")
        print("\n💡 请确保:")
        print("   1. FastAPI 应用正在运行: uv run uvicorn app.main:app --reload")
        print("   2. 数据库已启动: docker-compose up -d db")
        print("   3. 数据库已迁移: uv run alembic upgrade head")

    print("\n✨ 测试完成!")
